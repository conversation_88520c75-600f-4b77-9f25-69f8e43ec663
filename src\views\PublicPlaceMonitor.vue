<template>
    <div class="index-container">
        <TopNav />
        <div class="main-content">
            <SideNav @nav-click="handleNavClick" />
            <div class="content">
                <ContentTabs ref="contentTabsRef" />
                <div class="content-container">
                    <!-- 温度/噪音数据栏 -->
                    <div class="data-header">
                        <span class="data-title">温度/噪音数据</span>
                    </div>

                    <!-- 图表展示区域 -->
                    <div class="charts-container">
                        <div class="charts-column">
                            <!-- 折线图表组件 -->
                            <div class="chart-item">
                                <LineChartComponent :indicator-data="indicatorData" :chart-data="chartData"
                                    :height="230" @time-range-change="handleTimeRangeChange"
                                    @data-update="handleDataUpdate" />
                            </div>
                            <!-- 色带组件 -->
                            <div class="chart-item">
                                <ColorBandComponent :pm-value="pmData.value" :initial-date="pmData.date"
                                    @date-change="handlePmDateChange" @value-update="handlePmValueUpdate" />
                            </div>
                        </div>
                        <div class="charts-column">
                            <!-- 保留原有图片作为占位 -->
                            <div class="chart-item">
                                <GasMonitorComponent :co-value="gasData.co" :co2-value="gasData.co2"
                                    :initial-date="gasData.date" @date-change="handleGasDateChange"
                                    @data-update="handleGasDataUpdate" />
                            </div>
                            <div class="chart-item">
                                <FormaldehydeMonitorComponent :formaldehyde-value="formaldehydeData.value"
                                    :initial-date="formaldehydeData.date" @date-change="handleFormaldehydeDateChange"
                                    @value-update="handleFormaldehydeValueUpdate" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import TopNav from '../components/TopNav.vue';
import SideNav from '@/components/SideNav.vue';
import ContentTabs from '@/components/ContentTabs.vue';
import LineChartComponent from '@/components/LineChartComponent.vue';
import ColorBandComponent from '@/components/ColorBandComponent.vue';
import GasMonitorComponent from '@/components/GasMonitorComponent.vue';
import FormaldehydeMonitorComponent from '@/components/FormaldehydeMonitorComponent.vue';
// 组件引用
const contentTabsRef = ref(null)

// 指标数据
const indicatorData = reactive({
    temperature: 25.6,
    noise: 45.2,
    hcl: 0.8,
    humidity: 65.3,
    tvoc: 0.12
})

// 图表数据
const chartData = reactive({
    times: ['18:00', '18:01', '18:02', '18:03', '18:04', '18:05'],
    values: [25.6, 26.1, 25.8, 26.3, 25.9, 26.0]
})

// PM₁₀数据
const pmData = reactive({
    value: 88,
    date: '2025-07-01'
})

// 气体数据
const gasData = reactive({
    co: 12,
    co2: 36,
    date: '2025-07-01'
})

// 甲醛数据
const formaldehydeData = reactive({
    value: 32,
    date: '2025-07-01'
})


// 处理侧边栏导航点击
const handleNavClick = ({ index }) => {
    // 切换ContentTabs中的机构信息
    if (contentTabsRef.value) {
        contentTabsRef.value.switchInstitution(index)
    }
}

// 处理时间范围变化
const handleTimeRangeChange = (timeRange) => {
    console.log('时间范围变化:', timeRange)
    // 这里可以根据时间范围请求新的数据
    // 示例：更新指标数据
    updateIndicatorData(timeRange)
}

// 处理数据更新
const handleDataUpdate = (data) => {
    console.log('数据更新:', data)
}

// 处理PM日期变化
const handlePmDateChange = (date) => {
    console.log('PM日期变化:', date)
    pmData.date = date
    // 这里可以根据日期请求新的PM数据
    handlePmValueUpdate()
}

// 处理PM数值更新
const handlePmValueUpdate = (value) => {
    console.log('PM数值更新:', value)
    pmData.value = Math.floor(Math.random() * 301);
}

// 处理气体监测日期变化
const handleGasDateChange = (date) => {
    console.log('气体监测日期变化:', date)
    gasData.date = date
    // 这里可以根据日期请求新的气体数据
}

// 处理气体数据更新
const handleGasDataUpdate = (data) => {
    console.log('气体数据更新:', data)
}

// 处理甲醛监测日期变化
const handleFormaldehydeDateChange = (date) => {
    console.log('甲醛监测日期变化:', date)
    formaldehydeData.date = date
    // 这里可以根据日期请求新的甲醛数据
}

// 处理甲醛数值更新
const handleFormaldehydeValueUpdate = (value) => {
    console.log('甲醛数值更新:', value)
    formaldehydeData.value = value
}

// 更新指标数据（模拟数据更新）
const updateIndicatorData = (timeRange) => {
    // 根据时间范围生成不同的模拟数据
    const baseTemp = 25
    const variation = Math.random() * 5 - 2.5

    indicatorData.temperature = Number((baseTemp + variation).toFixed(1))
    indicatorData.noise = Number((45 + Math.random() * 10 - 5).toFixed(1))
    indicatorData.hcl = Number((0.8 + Math.random() * 0.4 - 0.2).toFixed(2))
    indicatorData.humidity = Number((65 + Math.random() * 10 - 5).toFixed(1))
    indicatorData.tvoc = Number((0.12 + Math.random() * 0.08 - 0.04).toFixed(3))
}
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.index-container {
    width: 100%;
    height: 100%;
    background: url(../assets/img/background.png);
    background-size: cover;
    background-repeat: no-repeat;
}

.main-content {
    display: flex;
    height: calc(100vh - 60px);
}

.content {
    width: calc(100vw - 350px);
    height: calc(100vh - 60px);
    // overflow: hidden;
    overflow-y: scroll;
    overflow-x: scroll;
}

.content-container {
    width: 100%;
    overflow: hidden;
    background-color: rgba(12, 25, 44, 0.86);
    padding: 10px;
    display: flex;
    flex-direction: column;
}

// 温度/噪音数据栏
.data-header {
    width: 100%;
    height: 57px;
    background: url(../assets/img/<EMAIL>) no-repeat;
    background-size: contain;
    background-position: left;
    display: flex;
    align-items: center;
    padding: 0 72px;
    margin-bottom: 20px;
}

.data-title {
    color: white;
    font-weight: normal;
    font-family: MicrosoftYaHei;
    font-size: 26px;
    color: #FFFFFF;
}

// 图表展示区域
.charts-container {
    width: calc(100% - 100px);
    margin: 0 auto;
    display: flex;
    gap: 12px;
    padding: 0 32px;
}

.charts-column {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.charts-column:first-child {
    flex-basis: 70%;
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.charts-column:last-child {
    flex-basis: 30%;
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.chart-image {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 8px;
}
</style>